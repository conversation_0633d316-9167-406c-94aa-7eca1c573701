package com.iflytek.lynxiao.support.config;


import com.iflytek.lynxiao.support.feign.LynxiaoFeignClientManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import skynet.boot.annotation.EnableSkynetSecurity;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.annotation.EnableSkynetPandora;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;
import skynet.boot.pandora.ogma.feign.OgmaFeignClientManager;
import skynet.boot.security.client.SignAuthFeignClientConfiguration;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@EnableScheduling
@EnableSkynetSwagger2
@EnableSkynetSecurity
@EnableSkynetPandora
@EnableSkynetPandoraOgma
@EnableFeignClients
@Configuration
@Import(SignAuthFeignClientConfiguration.class)
public class CommonAutoConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "lynxiao")
    public LynxiaoProperties lynxiaoProperties() {
        return new LynxiaoProperties();
    }

    @Bean
    public LynxiaoFeignClientManager lynxiaoFeignClientManager(LynxiaoProperties lynxiaoProperties, OgmaFeignClientManager ogmaFeignClientManager) {
        return new LynxiaoFeignClientManager(lynxiaoProperties, ogmaFeignClientManager);
    }

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.flow.analysis.health.special")
    public HealthSpecialItem healthSpecialItem() {
        return new HealthSpecialItem();
    }
}
