package com.iflytek.lynxiao.support.scheduled;


import com.iflytek.lynxiao.support.service.statistics.CalculateService;
import com.iflytek.lynxiao.support.service.statistics.MailStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * <p>
 * 数据统计【邮件流量、平台流量】
 */
@Log4j2
@Component
public class DataStatisticsScheduled {

    @Autowired
    private MailStatisticsService mailStatisticsService;

    @Autowired
    private CalculateService calculateService;

    @Value("${lynxiao.flow.analysis.mail.all.to}")
    private String mailAllTo;

    @Value("${lynxiao.flow.analysis.mail.simple.to}")
    private String mailSimpleTo;


    @Value("${lynxiao.flow.analysis.mail.cc}")
    private String mailCc;


    /**
     * 生成每日流量数据
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void generateFlowAnalysisData() {
        log.debug("生成每日流量数据begin------------------");
        long begin = System.currentTimeMillis();
        try {
            // 平台流量计算
            calculateService.statisticsAppTraffic(null);
            calculateService.statisticsBurialTraffic(null);

            // 动态埋点统计
            calculateService.statisticsDynamicsBurial(null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.debug("生成每日流量数据end，耗时: {}", System.currentTimeMillis() - begin);

    }


    /**
     * 发送每日流量数据
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendFlowAnalysisData() {
        log.debug("发送每日流量数据begin------------------");
        long begin = System.currentTimeMillis();
        LocalDateTime currentDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            //前一天
            LocalDateTime previousDate = currentDate.minusDays(1);
            // 前7天
            LocalDateTime previousDate7 = currentDate.minusDays(7);
            String previousDateString = previousDate.format(formatter);
            String previousDateString7 = previousDate7.format(formatter);
            mailStatisticsService.sendFlowAnalysisMail(previousDateString7, previousDateString, mailAllTo, mailCc, "ALL");
            mailStatisticsService.sendFlowAnalysisMail(previousDateString7, previousDateString, mailSimpleTo, mailCc, "SIMPLE");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.debug("发送每日流量数据end，耗时: {}", System.currentTimeMillis() - begin);
    }
}
