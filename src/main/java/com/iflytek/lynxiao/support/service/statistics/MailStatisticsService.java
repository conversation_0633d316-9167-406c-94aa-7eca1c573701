package com.iflytek.lynxiao.support.service.statistics;


import com.iflytek.lynxiao.support.dto.MailStatisticsDTO;
import org.jfree.data.category.DefaultCategoryDataset;

import java.util.Map;

/**
 *
 * 邮件流量统计
 * <AUTHOR>
 */
public interface MailStatisticsService {

    /**
     * 业务应用概览html
     * @param beginDate
     * @param endDate
     * @return
     */
    String generateAppMailHtml(String beginDate, String endDate, String type, Map<String, DefaultCategoryDataset> chartDatasets);

    /**
     * 产品方案概览html
     * @param beginDate
     * @param endDate
     * @return
     */
    String generateProductMailHtml(String beginDate, String endDate, String type);

    /**
     * 不同环境流量占比html
     * @param beginDate
     * @param endDate
     * @return
     */
    String generateProductEvnMailHtml(String beginDate, String endDate, String type);

    /**
     * 医疗场景标签流量占比统计html
     * @param beginDate
     * @param endDate
     * @return
     */
    String generateHealthMailHtml(String beginDate, String endDate, String type);


    /**
     * 发送流量统计邮件
     * @param beginDate
     * @param endDate
     * @param to
     * @param cc
     */
    void sendFlowAnalysisMail(String beginDate, String endDate, String to, String cc, String type);

}