package com.iflytek.lynxiao.support.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.dto.MailStatisticsDTO;
import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.dto.statistics.BurialDTO;
import com.iflytek.lynxiao.support.dto.statistics.FlowAnalysisMailDTO;
import com.iflytek.lynxiao.support.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.support.feign.region.MetaDictFeign;
import com.iflytek.lynxiao.support.repository.portal.ApplicationRepository;
import com.iflytek.lynxiao.support.repository.portal.FlowRepository;
import com.iflytek.lynxiao.support.repository.portal.FlowVersionRepository;
import com.iflytek.lynxiao.support.repository.portal.MetaRegionRepository;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlowVersion;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedMetaRegion;
import com.iflytek.lynxiao.support.repository.support.AppAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.BurialAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.FlowAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.entity.AppAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.BurialAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import com.iflytek.lynxiao.support.service.mail.MailService;
import com.iflytek.lynxiao.support.service.statistics.MailStatisticsService;
import com.iflytek.lynxiao.support.service.statistics.PlatformStatisticsService;
import com.iflytek.lynxiao.support.util.ChartUtils;
import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import skynet.boot.pandora.api.ApiResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
// todo 重构
public class MailStatisticsServiceImpl implements MailStatisticsService {

    public static final String SIMPLE = "SIMPLE";
    public static final String ALL = "ALL";

    @Resource
    private MetaRegionRepository metaRegionRepository;

    @Resource
    private ApplicationRepository applicationRepository;

    @Resource
    private FlowRepository flowRepository;

    @Resource
    private FlowVersionRepository flowVersionRepository;


    @Resource
    private MailService mailService;

    @Resource
    private LynxiaoFeignClientManager lynxiaoFeignClientManager;

    @Resource
    private PlatformStatisticsService platformStatisticsService;

    @Resource
    private AppAnalysisRepository appAnalysisRepository;

    @Resource
    private FlowAnalysisRepository flowAnalysisRepository;

    @Resource
    private BurialAnalysisRepository burialAnalysisRepository;


    @Override
    public void sendFlowAnalysisMail(String beginDate, String endDate, String to, String cc, String type) {

        String htmlConfig = ChartUtils.getHtmlStyle();
        // 1.1获取业务应用概览
        String appTableHtml = generateAppMailHtml(beginDate, endDate, type, new HashMap<>());

        htmlConfig = htmlConfig + appTableHtml;

        //1.2 医疗搜索按用户原始query维度的统计分析
        htmlConfig = htmlConfig + generateBizEqualHtml(beginDate, endDate, type);

        // 1.3 医疗搜索按策略名称的流量占比
        htmlConfig = htmlConfig + generateFlowNameHtml(beginDate, endDate, type);

        // 1.4 医疗搜索应用不同来源流量占比
        htmlConfig = htmlConfig + generateFromTypeHtml(beginDate, endDate, type);

        // 1.5 产品方案概览
        htmlConfig = htmlConfig + generateProductMailHtml(beginDate, endDate, type);

        // 1.6 医疗场景标签流量占比统计
        htmlConfig = htmlConfig + generateHealthMailHtml(beginDate, endDate, type);

        // 1.7 不同环境流量占比
        htmlConfig = htmlConfig + generateProductEvnMailHtml(beginDate, endDate, type);


        htmlConfig = htmlConfig + "</body></html>";

        String subject = "凌霄平台(Lynxiao)" + DateUtil.getSubjectDate(null) + "线上流量请求访问统计报表";
        if ("ALL".equals(type)) {
            subject = "凌霄平台(Lynxiao)" + DateUtil.getSubjectDate(null) + "全量请求访问统计报表";
        }
        mailService.sendWithHtml(to, cc, subject, htmlConfig);

    }

    private String generateBizEqualHtml(String beginDate, String endDate, String type) {

    }

    /**
     * 根据类型获取应用ID列表
     *
     * @param type SIMPLE 或 ALL
     * @return
     */
    private List<String> getAppIdByType(String type) {
        Map<String, GeneratedApplication> appIdMapByType = getAppIdMapByType(type);
        if (appIdMapByType == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(appIdMapByType.keySet());
    }

    private Map<String, GeneratedApplication> getAppIdMapByType(String type) {
        List<GeneratedApplication> generatedApplications = applicationRepository.findUnDeleted();

        Map<String, GeneratedApplication> appIdMap;
        if (SIMPLE.equals(type)) {
            appIdMap = generatedApplications.stream().filter(t -> "线上".equals(t.getType())).collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));
        } else {
            appIdMap = generatedApplications.stream().collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));
        }
        return appIdMap;
    }

    private String generateFlowNameHtml(String beginDate, String endDate, String type) {

        // 1. 获取线上环境code和线上类型的appId
        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().map(GeneratedMetaRegion::getCode).toList();
        List<String> appIds = getAppIdByType(type);
        String table = "";
        table = table + "<p>1.2 医疗搜索按策略名称的流量占比</p>";

        // 2. 获取flowName埋点数据
        List<BurialDTO> flowNameList = platformStatisticsService.exportBurialTraffic("flowName", beginDate, endDate, String.join(",", appIds), String.join(",", regionCodes));
        if (CollectionUtil.isEmpty(flowNameList)) {
            return table;
        }

        // 3. 组装html
        StringBuilder tableHtml = new StringBuilder();

        tableHtml.append("<table>")
                .append("<tr>")
                .append("<th>日期</th>")
                .append("<th>产品方案</th>")
                .append("<th>医疗策略名称</th>")
                .append("<th>总数</th>")
                .append("<th>有结果</th>")
                .append("<th>无结果</th>")
                .append("<th>有结果百分比</th>")
                .append("<th>流量占比</th>")
                .append("</tr>");

        for (BurialDTO dto : flowNameList) {
            String date = dto.getDate();
            List<BurialDTO.Product> products = dto.getDetails() != null ? dto.getDetails() : Collections.emptyList();

            // 计算当前日期的总行数（所有产品的埋点项数量之和）
            int dateRowspan = products.stream()
                    .mapToInt(p -> (p.getDetails() != null && !p.getDetails().isEmpty()) ? p.getDetails().size() : 0)
                    .sum();

            if (dateRowspan == 0) continue; // 跳过无数据的日期

            boolean isDateRowspanAdded = false;

            for (BurialDTO.Product product : products) {
                String productName = product.getProduct();
                List<BurialDTO.Burial> burials = product.getDetails() != null ? product.getDetails() : Collections.emptyList();
                int productRowspan = burials.size();

                if (productRowspan == 0) continue; // 跳过无埋点的产品

                boolean isProductRowspanAdded = false;

                for (BurialDTO.Burial burial : burials) {
                    tableHtml.append("<tr>");

                    // 添加日期列（仅第一次出现时添加）
                    if (!isDateRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(dateRowspan).append("'>").append(date).append("</td>");
                        isDateRowspanAdded = true;
                    }

                    // 添加产品列（仅第一次出现时添加）
                    if (!isProductRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(productRowspan).append("'>").append(productName).append("</td>");
                        isProductRowspanAdded = true;
                    }

                    // 添加埋点数据列
                    tableHtml.append("<td>").append(burial.getBurialItem()).append("</td>")
                            .append("<td>").append(burial.getRequestCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultCount()).append("</td>")
                            .append("<td>").append(burial.getNoResultCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultPercentage()).append("%</td>")
                            .append("<td>").append(burial.getTrafficPercentage()).append("%</td>");

                    tableHtml.append("</tr>");
                }
            }
        }

        tableHtml.append("</table><br>");
        return table + tableHtml.toString();
    }


    private String generateFromTypeHtml(String beginDate, String endDate, String type) {

        // 1. 获取线上环境code和线上类型的appId
        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().map(GeneratedMetaRegion::getCode).toList();
        List<String> appIds = getAppIdByType(type);

        String table = "";
        table = table + "<p>1.3 医疗搜索应用不同来源流量占比</p>";

        // 2. 获取query来源埋点数据
        List<BurialDTO> fromTypeList = platformStatisticsService.exportBurialTraffic("fromType", beginDate, endDate, String.join(",", appIds), String.join(",", regionCodes));
        if (CollectionUtil.isEmpty(fromTypeList)) {
            return table;
        }

        // 3. 组装html
        StringBuilder tableHtml = new StringBuilder();

        tableHtml.append("<table>")
                .append("<tr>")
                .append("<th>日期</th>")
                .append("<th>产品方案</th>")
                .append("<th>query来源</th>")
                .append("<th>总数</th>")
                .append("<th>有结果</th>")
                .append("<th>无结果</th>")
                .append("<th>有结果百分比</th>")
                .append("<th>流量占比</th>")
                .append("</tr>");

        for (BurialDTO dto : fromTypeList) {
            String date = dto.getDate();
            List<BurialDTO.Product> products = dto.getDetails() != null ? dto.getDetails() : Collections.emptyList();

            // 计算当前日期的总行数（所有产品的埋点项数量之和）
            int dateRowspan = products.stream()
                    .mapToInt(p -> (p.getDetails() != null && !p.getDetails().isEmpty()) ? p.getDetails().size() : 0)
                    .sum();

            if (dateRowspan == 0) continue; // 跳过无数据的日期

            boolean isDateRowspanAdded = false;

            for (BurialDTO.Product product : products) {
                String productName = product.getProduct();
                List<BurialDTO.Burial> burials = product.getDetails() != null ? product.getDetails() : Collections.emptyList();
                int productRowspan = burials.size();

                if (productRowspan == 0) continue; // 跳过无埋点的产品

                boolean isProductRowspanAdded = false;

                for (BurialDTO.Burial burial : burials) {
                    tableHtml.append("<tr>");

                    // 添加日期列（仅第一次出现时添加）
                    if (!isDateRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(dateRowspan).append("'>").append(date).append("</td>");
                        isDateRowspanAdded = true;
                    }

                    // 添加产品列（仅第一次出现时添加）
                    if (!isProductRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(productRowspan).append("'>").append(productName).append("</td>");
                        isProductRowspanAdded = true;
                    }

                    // 添加埋点数据列
                    tableHtml.append("<td>").append(burial.getBurialItem()).append("</td>")
                            .append("<td>").append(burial.getRequestCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultCount()).append("</td>")
                            .append("<td>").append(burial.getNoResultCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultPercentage()).append("%</td>")
                            .append("<td>").append(burial.getTrafficPercentage()).append("%</td>");

                    tableHtml.append("</tr>");
                }
            }
        }

        tableHtml.append("</table><br>");
        return table + tableHtml.toString();
    }


    @Override
    public String generateAppMailHtml(String beginDate, String endDate, String type, Map<String, DefaultCategoryDataset> chartDatasets) {

        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().filter(t -> t.getEnvType() == 2).map(GeneratedMetaRegion::getCode).toList();
        String table = "";
        table = table + "<p>1.1 业务应用概览</p>";
        table = table + "按照【业务应用】维度，仅统计线上环境的请求情况，包括请求总数、有/无结果数和有结果百分比等。<br><br>";
        table = table + "<table>" +

                "    <tr>" +
                "        <th >日期</th>" +
                "        <th >业务应用</th>" +
                "        <th >请求总数</th>" +
                "        <th >有结果</th>" +
                "        <th >无结果</th>" +
                "        <th >有结果百分比</th>" +
                "    </tr>";

        List<AppAnalysis> flowAnalysisMails = null;
        flowAnalysisMails = appAnalysisRepository.searchListBetweenDate(
                StringUtils.isNotBlank(beginDate) ? beginDate : DateUtil.getDate(null),
                StringUtils.isNotBlank(endDate) ? endDate : DateUtil.getDate(null),
                null, regionCodes, null);
        if (CollectionUtil.isEmpty(flowAnalysisMails)) {
            log.error("generateAppMailHtml 未获取到详细流量数据!");
            table = table + "</table><br>";
            return table;
        }

        Map<String, GeneratedApplication> generatedApplicationMap = getAppIdMapByType(ALL);

        // 按天分组
        Map<String, List<AppAnalysis>> dateFlowAnalysisMail = flowAnalysisMails.stream().collect(Collectors.groupingBy(AppAnalysis::getDate));

        // 使用TreeMap并自定义比较器来降序排序 按日期降序排列
        Map<String, List<MailStatisticsDTO>> resultMap = new TreeMap<>(Comparator.reverseOrder());

        for (Map.Entry<String, List<AppAnalysis>> entry : dateFlowAnalysisMail.entrySet()) {

            //某一个appid在某一天的查询条件map
            Map<String, List<AppAnalysis>> appIdRegionsMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(AppAnalysis::getAppId));

            //某一天的所有查询条件id 列表
            List<Long> appAnalysisIds = appIdRegionsMap.values().stream()
                    .flatMap(List::stream)
                    .map(AppAnalysis::getId)
                    .toList();

            // 根据appAnalysisIds查询流量详情  某一天的所有请求统计数据
            List<FlowAnalysis> flowAnalysisList = flowAnalysisRepository.findByRefIdIn(appAnalysisIds);
            // 获取所有appId一天的请求总量(所有环境)，用于计算流量占比
            long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();

            List<MailStatisticsDTO> resultList = new ArrayList<>();
            for (Map.Entry<String, List<AppAnalysis>> obj : appIdRegionsMap.entrySet()) {
                String appId = obj.getKey();
                if (null == generatedApplicationMap.get(appId)) {
                    continue;
                }

                //区分简版邮件还是全量邮件
                if (SIMPLE.equals(type)) {
                    if (!"线上".equals(generatedApplicationMap.get(appId).getType())) {
                        continue;
                    }
                }
                // 获取当前appId的所有环境的流量数据
                List<FlowAnalysis> flowAnalysisMailsByAppId = obj.getValue().stream()
                        .map(appAnalysis -> flowAnalysisList.stream()
                                .filter(flowAnalysis -> flowAnalysis.getRefId().equals(appAnalysis.getId()))
                                .findFirst().orElse(null))
                        .filter(Objects::nonNull).toList();

                // 所有环境的请求总数
                long tempRequestCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
                long tempHasResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getHasResult).sum();

                MailStatisticsDTO mailStatisticsDTO = new MailStatisticsDTO(entry.getKey(), null, appId, null, tempRequestCount, tempHasResultCount, tempRequestCount - tempHasResultCount, DataCalculateUtil.calculatePercentage(tempHasResultCount, tempRequestCount), DataCalculateUtil.calculatePercentage(tempRequestCount, totalRequestCount), null, generatedApplicationMap.get(appId).getName() + "(" + appId + ")");
                resultList.add(mailStatisticsDTO);
            }


            // 按照流量占比从高到低排序
            resultList.sort((o1, o2) -> o2.getTrafficPercentage().compareTo(o1.getTrafficPercentage()));

            resultMap.put(entry.getKey(), resultList);
        }


        try {
            // appId请求数量趋势图
            DefaultCategoryDataset requestDataset = new DefaultCategoryDataset();
            DefaultCategoryDataset successDataset = new DefaultCategoryDataset();
            for (Map.Entry<String, List<MailStatisticsDTO>> entry : resultMap.entrySet()) {
                table = table + "<tr>" +
                        "            <td rowspan= " + entry.getValue().size() + ">" + entry.getKey() + "</td>";

                for (MailStatisticsDTO dto : entry.getValue()) {
                    table = table +
                            "<td style=\"text-align: left;\">" + dto.getRemark() + "</td>" +
//                            "<td style=\"text-align: left;\">" + desc + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getRequestCount()) + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getHasResult()) + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getNoResult()) + "</td>" +
                            "<td>" + dto.getHasResultPercentage() + "%</td>" +
//                            "<td>" + dto.getTrafficPercentage() + "%</td>" +
                            "</tr>";

                    requestDataset.addValue(dto.getRequestCount(), dto.getRemark(), entry.getKey());
                    successDataset.addValue(dto.getHasResultPercentage(), dto.getRemark(), entry.getKey());

                }
            }

            chartDatasets.put("业务应用请求有结果率", successDataset);
            chartDatasets.put("业务应用请求" +
                    "趋势", requestDataset);


            table = table + "</table>";
            table = table + "<br><br>";

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return table;
    }

    @Override
    public String generateProductMailHtml(String beginDate, String endDate, String type) {

        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().filter(t -> t.getEnvType() == 2).map(GeneratedMetaRegion::getCode).collect(Collectors.toList());


        String table = "";

        table = table + "<p>1.4 产品方案概览</p>";
        table = table + "按照应用绑定的【产品方案】维度，仅统计线上环境的请求情况，包括请求总数、有/无结果数和有结果百分比等。<br><br>";

        table = table + "<table>" +

                "    <tr>" +
                "        <th >日期</th>" +
                "        <th >应用名称</th>" +
                "        <th >产品方案</th>" +
                "        <th >方案版本</th>" +
                "        <th >请求总数</th>" +
                "        <th >有结果</th>" +
                "        <th >无结果</th>" +
                "        <th >有结果百分比</th>" +
//                "        <th >流量占比</th>" +
                "    </tr>";

        List<AppAnalysis> flowAnalysisMails = null;
        flowAnalysisMails = appAnalysisRepository.searchListBetweenDate(
                StringUtils.isNotBlank(beginDate) ? beginDate : DateUtil.getDate(null),
                StringUtils.isNotBlank(endDate) ? endDate : DateUtil.getDate(null),
                null, regionCodes, null);
        if (CollectionUtil.isEmpty(flowAnalysisMails)) {
            log.error("generateProductMailHtml 未获取到详细流量数据!");
            table = table + "</table><br>";
            return table;
        }

        Map<String, GeneratedApplication> generatedApplicationMap = getAppIdMapByType(ALL);


        // 按天分组
        Map<String, List<AppAnalysis>> dateFlowAnalysisMail = flowAnalysisMails.stream().collect(Collectors.groupingBy(AppAnalysis::getDate));

        Map<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> htmlMap = new HashMap<>();

        for (Map.Entry<String, List<AppAnalysis>> dateEntry : dateFlowAnalysisMail.entrySet()) {

            String date = dateEntry.getKey();
            List<AppAnalysis> dateFlow = dateEntry.getValue();
            List<Long> appAnalysisIds = dateFlow.stream().map(AppAnalysis::getId).toList();

            // 根据appAnalysisIds查询流量详情
            List<FlowAnalysis> flowAnalysisList = flowAnalysisRepository.findByRefIdIn(appAnalysisIds);
            long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
            Map<Long, FlowAnalysis> flowAnalysisMap = flowAnalysisList.stream().collect(Collectors.toMap(FlowAnalysis::getRefId, t -> t));


            // key: appId,  value: [key:productId_productVersionId  value: FlowAnalysisMailDTO ]
            Map<String, Map<String, FlowAnalysisMailDTO>> finalAppFlowMap = new HashMap<>();
            // 根据业务应用分组
            Map<String, List<AppAnalysis>> appIdDateFlowMap = dateFlow.stream().collect(Collectors.groupingBy(AppAnalysis::getAppId));
            for (Map.Entry<String, List<AppAnalysis>> entry : appIdDateFlowMap.entrySet()) {

                String appId = entry.getKey();
                if ("SIMPLE".equals(type)) {
                    if (!"线上".equals(generatedApplicationMap.get(appId).getType())) {
                        continue;
                    }
                }
                // 根据产品方案和方案版本分组
                Map<String, List<AppAnalysis>> groupedMap = entry.getValue().stream()
                        .collect(Collectors.groupingBy(
                                flowAnalysisMail -> flowAnalysisMail.getProductId() + "_" + flowAnalysisMail.getProductVersionId()
                        ));
                Map<String, FlowAnalysisMailDTO> merge = new HashMap<>();
                for (Map.Entry<String, List<AppAnalysis>> bizCodeAndInMap : groupedMap.entrySet()) {


                    List<FlowAnalysis> flowAnalysisMailsByAppId = bizCodeAndInMap.getValue().stream()
                            .map(appAnalysis -> flowAnalysisMap.get(appAnalysis.getId()))
                            .filter(Objects::nonNull).toList();

                    long tempResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
                    long tempHasResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getHasResult).sum();
                    long tempNoResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getNoResult).sum();

                    FlowAnalysisMailDTO temp = new FlowAnalysisMailDTO();
                    temp.setRequestCount(tempResultCount);
                    temp.setHasResult(tempHasResultCount);
                    temp.setNoResult(tempNoResultCount);
                    temp.setHasResultPercentage(DataCalculateUtil.calculatePercentage(tempHasResultCount, tempResultCount));
                    temp.setBizCode(bizCodeAndInMap.getKey().split("_")[0]);
                    temp.setBizId(bizCodeAndInMap.getKey().split("_")[1]);

                    merge.put(bizCodeAndInMap.getKey(), temp);

                }
                GeneratedApplication generatedApplication = generatedApplicationMap.get(appId);

                finalAppFlowMap.put(generatedApplication.getName() + "(" + generatedApplication.getAppId() + ")", merge);

            }

            // 重新计算占比流量
            for (Map.Entry<String, Map<String, FlowAnalysisMailDTO>> obj : finalAppFlowMap.entrySet()) {
                Map<String, FlowAnalysisMailDTO> temp = obj.getValue();
                for (Map.Entry<String, FlowAnalysisMailDTO> objTemp : temp.entrySet()) {
                    objTemp.getValue().setTrafficPercentage(DataCalculateUtil.calculatePercentage(objTemp.getValue().getRequestCount(), totalRequestCount));
                }
            }

            Map<String, Map<String, List<FlowAnalysisMailDTO>>> result = new HashMap<>();
            for (Map.Entry<String, Map<String, FlowAnalysisMailDTO>> obj : finalAppFlowMap.entrySet()) {
                Map<String, FlowAnalysisMailDTO> tempBizCodeBizIdMap = obj.getValue();
                String appStr = obj.getKey();
                Map<String, List<FlowAnalysisMailDTO>> prodCodeMap = new HashMap<>();
                for (Map.Entry<String, FlowAnalysisMailDTO> sortObj : tempBizCodeBizIdMap.entrySet()) {
                    String productId = sortObj.getKey().split("_")[0];
                    if (prodCodeMap.containsKey(productId)) {
                        prodCodeMap.get(productId).add(sortObj.getValue());
                    } else {
                        List<FlowAnalysisMailDTO> list = new ArrayList<>();
                        list.add(sortObj.getValue());
                        prodCodeMap.put(productId, list);
                    }

                }
                result.put(appStr, prodCodeMap);
            }
            htmlMap.put(date, result);

        }


        System.out.println(JSON.toJSONString(htmlMap));
        if (htmlMap.size() > 0) {
            // 使用TreeMap并自定义比较器来降序排序
            Map<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> sortedMapHtml = new TreeMap<>(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    // 降序排序
                    return o2.compareTo(o1);
                }
            });
            sortedMapHtml.putAll(htmlMap);


            for (Map.Entry<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> dateEntry : sortedMapHtml.entrySet()) {
                String date = dateEntry.getKey();
                Map<String, Map<String, List<FlowAnalysisMailDTO>>> appMap = dateEntry.getValue();
                int dateRowSpan = appMap.values().stream().flatMapToInt(productMap -> productMap.values().stream().mapToInt(List::size)).sum();


                int currentDateRowSpan = dateRowSpan; // 用于跟踪当前日期行的rowspan
                for (Map.Entry<String, Map<String, List<FlowAnalysisMailDTO>>> appEntry : appMap.entrySet()) {
                    String appName = appEntry.getKey();
                    Map<String, List<FlowAnalysisMailDTO>> productMap = appEntry.getValue();
                    int appRowSpan = productMap.values().stream().mapToInt(List::size).sum();

                    int currentAppRowSpan = appRowSpan; // 用于跟踪当前应用名称行的rowspan
                    for (Map.Entry<String, List<FlowAnalysisMailDTO>> productEntry : productMap.entrySet()) {
                        String productCode = productEntry.getKey();
                        List<FlowAnalysisMailDTO> flowAnalysisList = productEntry.getValue();

                        int productRowSpan = flowAnalysisList.size();

                        CollectionUtil.sort(flowAnalysisList, new Comparator<FlowAnalysisMailDTO>() {
                            @Override
                            public int compare(FlowAnalysisMailDTO o1, FlowAnalysisMailDTO o2) {
                                return o2.getTrafficPercentage().compareTo(o1.getTrafficPercentage());
                            }
                        });
                        for (FlowAnalysisMailDTO flowAnalysis : flowAnalysisList) {
                            // 添加日期行
                            if (currentDateRowSpan == dateRowSpan) {
                                table += "<tr><td rowspan=\"" + dateRowSpan + "\">" + date + "</td>";
                                currentDateRowSpan -= productRowSpan;
                            } else {
                                table += "<tr>";
                            }

                            // 添加应用名称行
                            if (currentAppRowSpan == appRowSpan) {
                                table += "<td style=\"text-align: left;\" rowspan=\"" + appRowSpan + "\">" + appName + "</td>";
                                currentAppRowSpan -= productRowSpan;
                            }

                            // 添加产品方案行
                            if (flowAnalysisList.indexOf(flowAnalysis) == 0) {
                                Optional<GeneratedFlow> flow = flowRepository.findById(Long.valueOf(productCode));
                                table += "<td style=\"text-align: left;\"  rowspan=\"" + productRowSpan + "\">" + flow.get().getName() + "(" + flow.get().getCode() + ")" + "</td>";
                            }

                            // 添加方案版本、请求总数等数据
                            Optional<GeneratedFlowVersion> flowVersion = flowVersionRepository.findById(Long.valueOf(flowAnalysis.getBizId()));
                            table += "<td style=\"text-align: left;\">" + flowVersion.get().getName() + "(V" + String.format("%03d", flowVersion.get().getVersion()) + ")" + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getRequestCount()) + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getHasResult()) + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getNoResult()) + "</td>" +
                                    "<td>" + flowAnalysis.getHasResultPercentage() + "%</td>" +
//                                    "<td>" + flowAnalysis.getTrafficPercentage() + "%</td>" +
                                    "</tr>";
                        }
                    }
                }
            }
        }

        table = table + "</table>";
        table = table + "<br><br>";

        return table;
    }

    @Override
    public String generateProductEvnMailHtml(String beginDate, String endDate, String type) {

        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().map(GeneratedMetaRegion::getCode).collect(Collectors.toList());


        String table = "";
        table = table + "<p>1.6 不同环境流量占比</p>";
        table = table + "按照【环境来源】维度，统计线上环境和验证环境的流量占比。<br><br>";
        List<AppAnalysis> flowAnalysisMails = null;
        flowAnalysisMails = appAnalysisRepository.searchListBetweenDate(
                StringUtils.isNotBlank(beginDate) ? beginDate : DateUtil.getDate(null),
                StringUtils.isNotBlank(endDate) ? endDate : DateUtil.getDate(null),
                null, regionCodes, null);
        if (CollectionUtil.isEmpty(flowAnalysisMails)) {
            log.error("generateProductEvnMailHtml 未获取到详细流量数据!");
            return table;
        }

        List<Long> appAnalysisIds = flowAnalysisMails.stream()
                .map(AppAnalysis::getId)
                .toList();

        // 根据appAnalysisIds查询流量详情
        List<FlowAnalysis> flowAnalysisList = flowAnalysisRepository.findByRefIdIn(appAnalysisIds);
        Map<Long, FlowAnalysis> flowAnalysisMap = flowAnalysisList.stream().collect(Collectors.toMap(FlowAnalysis::getRefId, t -> t));

        List<GeneratedApplication> generatedApplications = applicationRepository.findUnDeleted();
        Map<String, GeneratedApplication> generatedApplicationMap = generatedApplications.stream().collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));


        table = table + "<table>" +

                "    <tr>" +
                "        <th rowspan = 2>日期</th>" +
                "        <th rowspan = 2>应用名称</th>" +
                "        <th colspan = 4>北京生产环境</th>" +
                "        <th colspan = 4>上海生产环境</th>" +
                "        <th colspan = 4>合肥验证环境</th>";
        table = table + "</tr>";

        table = table + "<tr>";
        table = table +
                "    <th >总数</th>" +
                "    <th >有结果</th>" +
                "    <th >无结果</th>" +
                "    <th >有结果百分比</th>" +
                "    <th >总数</th>" +
                "    <th >有结果</th>" +
                "    <th >无结果</th>" +
                "    <th >有结果百分比</th>" +
                "    <th >总数</th>" +
                "    <th >有结果</th>" +
                "    <th >无结果</th>" +
                "    <th >有结果百分比</th>";

        table = table + "</tr>";

        Map<String, List<AppAnalysis>> flowDateMap = null;

        if ("SIMPLE".equals(type)) {
            flowDateMap = flowAnalysisMails.stream().filter(t -> "线上".equals(generatedApplicationMap.get(t.getAppId()).getType())).collect(Collectors.groupingBy(AppAnalysis::getDate));
        } else {
            flowDateMap = flowAnalysisMails.stream().collect(Collectors.groupingBy(AppAnalysis::getDate));
        }
        // 使用TreeMap并自定义比较器来降序排序
        Map<String, List<AppAnalysis>> sortedMapHtml = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                // 降序排序
                return o2.compareTo(o1);
            }
        });
        sortedMapHtml.putAll(flowDateMap);
        for (Map.Entry<String, List<AppAnalysis>> entry : sortedMapHtml.entrySet()) {

            String date = entry.getKey();
            // appId分组   key:appId  value:某天各个环境流量集合
            Map<String, List<AppAnalysis>> appIdRegionMap = entry.getValue().stream().collect(Collectors.groupingBy(
                    AppAnalysis::getAppId
            ));

            table = table + "<tr>";
            table = table +
                    "    <td rowspan= " + appIdRegionMap.size() + ">" + date + "</td>";


            for (Map.Entry<String, List<AppAnalysis>> appIdMap : appIdRegionMap.entrySet()) {

                String appId = appIdMap.getKey();

                table = table +
                        "    <td>" + generatedApplicationMap.get(appId).getName() + "(" + generatedApplicationMap.get(appId).getAppId() + ")" + "</td>";

                // 根据环境分组
                Map<String, List<AppAnalysis>> groupByRegion = appIdMap.getValue().stream().collect(Collectors.groupingBy(AppAnalysis::getRegion));
                List<String> regionList = Arrays.asList(RegionCode.DX, RegionCode.SH, RegionCode.HF);
                for (String region : regionList) {
                    if (null != groupByRegion.get(region)) {
                        List<AppAnalysis> appAnalyses = groupByRegion.get(region);

                        List<FlowAnalysis> flowAnalysisMailsByAppId = appAnalyses.stream()
                                .map(appAnalysis -> flowAnalysisMap.get(appAnalysis.getId()))
                                .filter(Objects::nonNull).toList();

                        long tempResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
                        long tempHasResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getHasResult).sum();
                        long tempNoResultCount = flowAnalysisMailsByAppId.stream().mapToLong(FlowAnalysis::getNoResult).sum();

                        table = table +
                                "    <td >" + DataCalculateUtil.getFormatNumber(tempResultCount) + "</td>" +
                                "    <td >" + DataCalculateUtil.getFormatNumber(tempHasResultCount) + "</td>" +
                                "    <td >" + DataCalculateUtil.getFormatNumber(tempNoResultCount) + "</td>" +
                                "    <td >" + DataCalculateUtil.calculatePercentage(tempHasResultCount, tempResultCount) + "%</td>";
                    } else {
                        table = table +
                                "    <td >--</td>" +
                                "    <td >--</td>" +
                                "    <td >--</td>" +
                                "    <td >--</td>";
                    }
                }
                table = table + "<tr>";
            }
        }

        table = table + "</table>";
        table = table + "<br>";

        return table;
    }

    @Override
    public String generateHealthMailHtml(String beginDate, String endDate, String type) {


        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        List<String> regionCodes = regions.stream().filter(t -> t.getEnvType() == 2).map(GeneratedMetaRegion::getCode).collect(Collectors.toList());

        String table = "";

        table = table + "<p>1.5 医疗场景标签流量占比统计</p>";
        table = table + "按【query对应的场景标签】维度，仅统计线上环境的请求情况，包括请求总数、有/无结果数和有结果百分比等，仅展示流量占比超过 3% 的场景标签。<br><br>";

        table = table + "<table>" +

                "    <tr>" +
                "        <th >日期</th>" +
                "        <th >场景标签</th>" +
                "        <th >总数</th>" +
                "        <th >有结果</th>" +
                "        <th >无结果</th>" +
                "        <th >有结果百分比</th>" +
                "        <th >流量占比</th>" +
                "    </tr>";

        List<String> appIds = getAppIdByType(type);

        List<BurialAnalysis> flowAnalysisMails = null;
        flowAnalysisMails = burialAnalysisRepository.searchListBetweenDate("scene",
                StringUtils.isNotBlank(beginDate) ? beginDate : DateUtil.getDate(null),
                StringUtils.isNotBlank(endDate) ? endDate : DateUtil.getDate(null),
                regionCodes, null, appIds);
        if (CollectionUtil.isEmpty(flowAnalysisMails)) {
            log.error("generateHealthMailHtml 未获取到详细流量数据!");
            table = table + "</table><br>";
            return table;
        }

        List<Long> burialAnalysisIds = flowAnalysisMails.stream()
                .map(BurialAnalysis::getId)
                .toList();

        // 根据appAnalysisIds查询流量详情
        List<FlowAnalysis> flowAnalysisList = flowAnalysisRepository.findByRefIdIn(burialAnalysisIds);
        Map<Long, FlowAnalysis> flowAnalysisMap = flowAnalysisList.stream().collect(Collectors.toMap(FlowAnalysis::getRefId, t -> t));


        // 查询医疗场景标签
        //todo 为啥是从上海读？
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.SH);
        ApiResponse apiResponse = metaDictFeign.getListByCode("health-query-scene");
        List<GeneratedMetaDict> healthQuerySceneList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
        Map<String, GeneratedMetaDict> generatedMetaDictMap = healthQuerySceneList.stream().collect(Collectors.toMap(GeneratedMetaDict::getName, t -> t));

        Map<String, List<BurialAnalysis>> dateFlowAnalysisMail = flowAnalysisMails.stream().collect(Collectors.groupingBy(BurialAnalysis::getDate));
        // 使用TreeMap并自定义比较器来降序排序
        Map<String, List<BurialAnalysis>> sortedMap = new TreeMap<>(Comparator.reverseOrder());
        sortedMap.putAll(dateFlowAnalysisMail);


        for (Map.Entry<String, List<BurialAnalysis>> dateEntry : sortedMap.entrySet()) {
            // 创建一个临时地图，用于按 bizCode 合并统计结果
            Map<String, FlowAnalysisMailDTO> mergedMap = new LinkedHashMap<>();
            long dayTotal = dateEntry.getValue().stream()
                    .mapToLong(burialAnalysis -> flowAnalysisMap.get(burialAnalysis.getId()).getRequestCount())
                    .sum();


            // 过滤一天内流量占比大于3%的标签
            List<BurialAnalysis> filteredList = dateEntry.getValue().stream()
                    .filter(burialAnalysis -> {
                        FlowAnalysis flowAnalysis = flowAnalysisMap.get(burialAnalysis.getId());
                        return flowAnalysis != null && DataCalculateUtil.calculatePercentage(flowAnalysis.getRequestCount(), dayTotal).compareTo(BigDecimal.valueOf(3)) > 0;
                    })
                    .toList();
            if (CollectionUtil.isEmpty(filteredList)) {
                continue;
            }

            // 遍历当前日期的 FlowAnalysisMailDTO 列表
            for (BurialAnalysis burialAnalysis : filteredList) {
                String item = burialAnalysis.getBurialItem();
                if (mergedMap.containsKey(item)) {
                    // 如果已经存在相同的 bizCode，则合并统计结果
                    FlowAnalysis flowAnalysisMail = flowAnalysisMap.get(burialAnalysis.getId());

                    FlowAnalysisMailDTO existingMail = mergedMap.get(item);
                    existingMail.setRequestCount(existingMail.getRequestCount() + flowAnalysisMail.getRequestCount());
                    existingMail.setHasResult(existingMail.getHasResult() + flowAnalysisMail.getHasResult());
                    existingMail.setNoResult(existingMail.getNoResult() + flowAnalysisMail.getNoResult());
                    // 更新百分比（需要重新计算）
                    existingMail.setHasResultPercentage(DataCalculateUtil.calculatePercentage(existingMail.getHasResult(), existingMail.getRequestCount()));
                    existingMail.setTrafficPercentage(DataCalculateUtil.calculatePercentage(existingMail.getRequestCount(), dayTotal));
                } else {
                    // 如果不存在相同的 bizCode，则直接添加到临时地图
                    FlowAnalysis flowAnalysisMail = flowAnalysisMap.get(burialAnalysis.getId());
                    FlowAnalysisMailDTO flowAnalysisMailDTO = BeanUtil.copyProperties(flowAnalysisMail, FlowAnalysisMailDTO.class);
                    flowAnalysisMailDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(flowAnalysisMail.getHasResult(), flowAnalysisMail.getRequestCount()));
                    flowAnalysisMailDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(flowAnalysisMail.getRequestCount(), dayTotal));
                    flowAnalysisMailDTO.setBizCode(item);
                    mergedMap.put(item, flowAnalysisMailDTO);
                }
            }

            // 将临时地图的值转换回列表
            List<FlowAnalysisMailDTO> mergedList = new ArrayList<>(mergedMap.values());

            // 按流量占比从高到低排序
            mergedList.sort((o1, o2) -> o2.getTrafficPercentage().compareTo(o1.getTrafficPercentage()));

            // 生成表格行
            table = table + "<tr>" +
                    "            <td rowspan= " + mergedList.size() + ">" + dateEntry.getKey() + "</td>";

            for (FlowAnalysisMailDTO flowAnalysisMail : mergedList) {
                String name = "未知";
                if (null != generatedMetaDictMap.get(flowAnalysisMail.getBizCode())) {
                    name = generatedMetaDictMap.get(flowAnalysisMail.getBizCode()).getName();
                }
                table = table +
                        "<td style=\"text-align: left;\">" + name + "</td>" +
                        "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysisMail.getRequestCount()) + "</td>" +
                        "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysisMail.getHasResult()) + "</td>" +
                        "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysisMail.getNoResult()) + "</td>" +
                        "<td>" + flowAnalysisMail.getHasResultPercentage() + "%</td>" +
                        "<td>" + flowAnalysisMail.getTrafficPercentage() + "%</td>" +
                        "</tr>";
            }
        }


        table = table + "</table><br>";
        return table;
    }
}
