package com.iflytek.lynxiao.support.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.lynxiao.support.consts.BurialStatisticsModel;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.consts.StatisticsModule;
import com.iflytek.lynxiao.support.dto.StatisticsConditionContext;
import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.support.feign.region.MetaDictFeign;
import com.iflytek.lynxiao.support.repository.portal.*;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlowVersion;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedMetaRegion;
import com.iflytek.lynxiao.support.repository.support.*;
import com.iflytek.lynxiao.support.repository.support.entity.AppAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.BurialAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.BurialDynamicsAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import com.iflytek.lynxiao.support.service.elastic.ElasticSearchService;
import com.iflytek.lynxiao.support.service.statistics.CalculateService;
import com.iflytek.lynxiao.support.util.AuditingEntityUtil;
import com.iflytek.lynxiao.support.util.DateUtil;
import com.iflytek.lynxiao.support.util.EsConditionUtil;
import com.iflytek.lynxiao.support.util.StatisticsUtil;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import skynet.boot.pandora.api.ApiResponse;

import java.util.*;
import java.util.stream.Collectors;

@Transactional
@Log4j2
@Service
public class CalculateServiceImpl implements CalculateService {
    @Autowired
    private FlowVersionRepository flowVersionRepository;

    @Autowired
    private AppAnalysisRepository appAnalysisRepository;

    @Autowired
    private BurialAnalysisRepository burialAnalysisRepository;

    @Autowired
    private FlowAnalysisRepository flowAnalysisRepository;

    @Resource
    private MetaRegionRepository metaRegionRepository;

    @Resource
    private ApplicationRepository applicationRepository;

    @Resource
    private EsConditionUtil esConditionUtil;

    @Resource
    private ElasticSearchService elasticSearchService;

    @Resource
    private LynxiaoFeignClientManager lynxiaoFeignClientManager;

    @Resource
    private BurialDynamicsRepository burialDynamicsRepository;

    @Resource
    private BusinessEqualIdAnalysisRepository bizEqualIdAnalysisRepository;

    @Resource
    private StatisticsUtil statisticsUtil;


    @Override
    public void statisticsAppTraffic(String date) {
        log.info("开始统计业务应用流量数据，日期: {}", date);
        // 1. 清理历史数据
        cleanAppHistoryData(DateUtil.getDate(date));

        //2. 获取查询条件
        String indexName = DateUtil.getIndexName(date);
        log.info("获取索引名称: {}", indexName);
        List<StatisticsConditionContext> conditionContexts = getStatisticsConditionContextList(indexName);

        //3. 从elk中获取统计数据
        for (StatisticsConditionContext conditionContext : conditionContexts) {
            long totalRequest = esConditionUtil.queryTotalNum(conditionContext.getRegion(),
                    indexName,
                    StatisticsModule.PRO_CODE.getCode(),
                    String.valueOf(conditionContext.getProductVersionId()),
                    conditionContext.getAppId(), null, null);
            conditionContext.setTotalNum(totalRequest);

            if (totalRequest > 0) {
                long hasResultNum = esConditionUtil.queryHasResultNum(conditionContext.getRegion(),
                        indexName,
                        StatisticsModule.PRO_CODE.getCode(),
                        String.valueOf(conditionContext.getProductVersionId()),
                        conditionContext.getAppId(), null, null);
                conditionContext.setHasResultNum(hasResultNum);

                long successNum = esConditionUtil.querySuccessNum(conditionContext.getRegion(),
                        indexName,
                        StatisticsModule.PRO_CODE.getCode(),
                        String.valueOf(conditionContext.getProductVersionId()),
                        conditionContext.getAppId());
                conditionContext.setSuccessNum(successNum);
            }
        }

        //4. 保存查询的结果
        saveAppAnalysisData(DateUtil.getDate(date), conditionContexts);
        log.info("统计业务应用流量数据完成，日期: {}, 条件列表: {}", date, conditionContexts.size());
    }

    @Override
    public void statisticsBurialTraffic(String date) {
        log.info("开始统计埋点流量数据，日期: {}", date);
        // 1. 清理历史数据
        cleanBurialHistoryData(DateUtil.getDate(date));

        //2. 构造查询列表
        String indexName = DateUtil.getIndexName(date);
        log.info("获取索引名称: {}", indexName);

        List<BurialType> burialTypes = new ArrayList<>();
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        Arrays.asList(BurialStatisticsModel.ONE.getCode(), BurialStatisticsModel.TWO.getCode(), BurialStatisticsModel.THREE.getCode()).forEach(t -> {
            ApiResponse apiResponse = metaDictFeign.getListByCode(t);
            List<GeneratedMetaDict> itemList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
            itemList.forEach(item -> burialTypes.add(new BurialType(item.getCode())));
        });

        List<GeneratedMetaRegion> regions = getRegions();

        for (GeneratedMetaRegion region : regions) {
            burialTypes.forEach(item -> {
                // 查询ES得到每个模板埋点类型下的埋点值列表
                List<String> burialItemList = elasticSearchService.aggregationQuery(region.getCode(), indexName, item.getCode());
                if (CollectionUtil.isEmpty(burialItemList)) {
                    return;
                }
                item.setValueList(burialItemList);
            });
        }

        List<StatisticsConditionContext> conditionContexts = new ArrayList<>();

        List<StatisticsConditionContext> regionAppIdPvidCondList = getStatisticsConditionContextList(indexName);
        String regionAppIdPvidCondListStr = JSONArray.toJSONString(regionAppIdPvidCondList);
        for (BurialType burialTypeItem : burialTypes) {
            burialTypeItem.getValueList().forEach(burialItem -> {
                List<StatisticsConditionContext> newRegionAppIdPvidCondList = JSONArray.parseArray(regionAppIdPvidCondListStr, StatisticsConditionContext.class);
                newRegionAppIdPvidCondList.forEach(item -> {
                    item.setBurialType(burialTypeItem.getCode());
                    item.setBurialItem(burialItem);
                });
                conditionContexts.addAll(newRegionAppIdPvidCondList);
            });
        }

        //3. 从elk中获取统计数据
        for (StatisticsConditionContext conditionContext : conditionContexts) {

            long totalRequest = esConditionUtil.queryTotalNum(
                    conditionContext.getRegion(),
                    indexName,
                    StatisticsModule.BURIAL.getCode(),
                    conditionContext.getBurialItem(),
                    String.valueOf(conditionContext.getProductVersionId()),
                    conditionContext.getBurialType(),
                    conditionContext.getAppId()
            );
            conditionContext.setTotalNum(totalRequest);
            if (totalRequest <= 0) {
                continue;
            }

            long hasResultCount = esConditionUtil.queryHasResultNum(
                    conditionContext.getRegion(),
                    indexName,
                    StatisticsModule.BURIAL.getCode(),
                    conditionContext.getBurialItem(),
                    String.valueOf(conditionContext.getProductVersionId()),
                    conditionContext.getBurialType(),
                    conditionContext.getAppId()
            );
            conditionContext.setHasResultNum(hasResultCount);
        }

        //4.保存结果
        saveBurialAnalysisData(DateUtil.getDate(date), conditionContexts);

        log.info("统计埋点流量数据完成，日期: {}, 条件列表: {}", date, conditionContexts.size());
    }


    @Override
    public void statisticsDynamicsBurial(String date) {
        log.info("开始统计动态埋点流量数据，日期: {}", date);
        // 1. 清理历史数据
        cleanBurialDynamicsHistoryData(DateUtil.getDate(date));
        // 2. 获取基础数据
        String indexName = DateUtil.getIndexName(date);
        log.info("获取索引名称: {}", indexName);

        List<CombineBurial> burialCombination = getBurialCombination(indexName);

        for (CombineBurial combineBurial : burialCombination) {
            List<List<Map.Entry<String, String>>> lists = generateCartesianCombinations(combineBurial.burialTypes);
            combineBurial.setCartesian(lists);
        }

        List<StatisticsConditionContext> statisticsConditionContextList = new ArrayList<>();
        List<StatisticsConditionContext> cartesian4RegionAppIdPvidSCCList = getStatisticsConditionContextList(indexName);
        burialCombination.forEach(item -> {
            for (List<Map.Entry<String, String>> entries : item.getCartesian()) {
                //todo 为啥要copy一份
                for (StatisticsConditionContext regionAppIdPvidSCC : cartesian4RegionAppIdPvidSCCList) {
                    StatisticsConditionContext statisticsConditionContext1 = BeanUtil.copyProperties(regionAppIdPvidSCC, StatisticsConditionContext.class);
                    statisticsConditionContext1.setDynamicConditions(entries);
                    statisticsConditionContext1.setBurialType(item.getCode());
                    statisticsConditionContextList.add(statisticsConditionContext1);
                }
            }
        });

        // 3. 从elk中获取统计数据
        queryAndSave(date, statisticsConditionContextList);
        log.info("统计动态埋点流量数据完成，日期: {}, 条件列表: {}", date, statisticsConditionContextList.size());
    }

    @Override
    public void statisticsBizEqualId(String date) {
        log.info("开始统计业务唯一标识流量数据，日期: {}", date);
        // 1. 清理历史数据
        cleanBizEqualIdHistoryData(DateUtil.getDate(date));
        // 2. 获取基础数据
        String indexName = DateUtil.getIndexName(date);
        log.info("获取索引名称: {}", indexName);

        List<StatisticsConditionContext> bizEqualIdConditionContextList = getBizEqualIdConditionContextList(indexName);

        //3. 从elk中获取统计数据
        for (StatisticsConditionContext conditionContext : bizEqualIdConditionContextList) {
            long totalRequest = esConditionUtil.queryTotalNum(conditionContext.getRegion(),
                    indexName,
                    StatisticsModule.BIZ_EQUAL_ID.getCode(),
                    conditionContext.getBizId(),
                    conditionContext.getAppId(), null, null);
            conditionContext.setTotalNum(totalRequest);

            if (totalRequest > 0) {
                long hasResultNum = esConditionUtil.queryHasResultNum(conditionContext.getRegion(),
                        indexName,
                        StatisticsModule.BIZ_EQUAL_ID.getCode(),
                        conditionContext.getBizId(),
                        conditionContext.getAppId(), null, null);
                conditionContext.setHasResultNum(hasResultNum);

                long successNum = esConditionUtil.querySuccessNum(conditionContext.getRegion(),
                        indexName,
                        StatisticsModule.BIZ_EQUAL_ID.getCode(),
                        conditionContext.getBizId(),
                        conditionContext.getAppId());
                conditionContext.setSuccessNum(successNum);
            }
        }

    }

    private void saveAppAnalysisData(String date, List<StatisticsConditionContext> conditionContexts) {

        for (StatisticsConditionContext conditionContext : conditionContexts) {
            if (conditionContext.getTotalNum() <= 0) {
                continue;
            }

            // 保存埋点分析数据
            AppAnalysis appAnalysis = BeanUtil.copyProperties(conditionContext, AppAnalysis.class);
            appAnalysis.setDate(date);
            AuditingEntityUtil.fillCreateValue(appAnalysis);
            appAnalysisRepository.save(appAnalysis);

            // 保存流量分析数据
            FlowAnalysis flowAnalysis = new FlowAnalysis();
            flowAnalysis.setRefId(appAnalysis.getId());
            flowAnalysis.setRequestCount(conditionContext.getTotalNum());
            flowAnalysis.setHasResult(conditionContext.getHasResultNum());
            flowAnalysis.setNoResult(conditionContext.getTotalNum() - conditionContext.getHasResultNum());
            flowAnalysis.setSuccessCount(conditionContext.getSuccessNum());
            AuditingEntityUtil.fillCreateValue(flowAnalysis);
            flowAnalysisRepository.save(flowAnalysis);
        }
    }

    private void saveBurialAnalysisData(String date, List<StatisticsConditionContext> conditionContexts) {

        for (StatisticsConditionContext conditionContext : conditionContexts) {
            if (conditionContext.getTotalNum() <= 0) {
                continue;
            }

            // 保存埋点分析数据
            BurialAnalysis burialAnalysis = BeanUtil.copyProperties(conditionContext, BurialAnalysis.class);
            burialAnalysis.setDate(date);
            AuditingEntityUtil.fillCreateValue(burialAnalysis);
            burialAnalysisRepository.save(burialAnalysis);

            // 保存流量分析数据
            FlowAnalysis flowAnalysis = new FlowAnalysis();
            flowAnalysis.setRefId(burialAnalysis.getId());
            flowAnalysis.setRequestCount(conditionContext.getTotalNum());
            flowAnalysis.setHasResult(conditionContext.getHasResultNum());
            flowAnalysis.setNoResult(conditionContext.getTotalNum() - conditionContext.getHasResultNum());
            flowAnalysis.setSuccessCount(conditionContext.getSuccessNum());
            AuditingEntityUtil.fillCreateValue(flowAnalysis);
            flowAnalysisRepository.save(flowAnalysis);
        }
    }

    /**
     * 获取 region、 appId、 pvid 三者笛卡尔积后的查询条件列表
     *
     * @param indexName es索引名
     */
    private List<StatisticsConditionContext> getStatisticsConditionContextList(String indexName) {

        List<StatisticsConditionContext> conditionContexts = new ArrayList<>();
        List<GeneratedFlowVersion> flowVersionList = this.flowVersionRepository.findAllUndeleted();
        for (GeneratedMetaRegion region : getRegions()) {
            List<String> pvidList = elasticSearchService.aggregationQuery(region.getCode(), indexName, "pvid");
            for (String appId : getAppIds()) {
                log.debug("获取应用: {}, 区域: {}, pvid列表: {}", appId, region.getCode(), pvidList);

                //去除所有pvid不为数字的情况
                pvidList = pvidList.stream()
                        .filter(StringUtils::isNumeric)
                        .distinct()
                        .toList();

                pvidList.forEach(pvid -> {

                    StatisticsConditionContext context = new StatisticsConditionContext()
                            .setAppId(appId)
                            .setRegion(region.getCode())
                            .setProductVersionId(pvid);

                    flowVersionList.stream()
                            .filter(flowVersion -> flowVersion.getId().equals(Long.valueOf(pvid)))
                            .findFirst().ifPresent(flowVersion -> context.setProductId(String.valueOf(flowVersion.getFlowId())));

                    //过滤掉未查找到产品方案的异常数据
                    if (StringUtils.isBlank(context.getProductId())) {
                        log.warn("未找到对应的产品方案版本: {}", pvid);
                    } else {
                        conditionContexts.add(context);
                    }
                });

            }
        }
        log.info("获取查询条件列表: {} 条", conditionContexts.size());

        return conditionContexts;
    }

    /**
     * 获取业务唯一标识id、 appId、 region 、 pvid查询条件列表
     *
     * @param indexName
     * @return
     */
    private List<StatisticsConditionContext> getBizEqualIdConditionContextList(String indexName) {
        List<StatisticsConditionContext> conditionContexts = new ArrayList<>();

        for (GeneratedMetaRegion region : getRegions()) {
            List<String> pvidList = elasticSearchService.aggregationQuery(region.getCode(), indexName, "pvid");
            for (String appId : getAppIds()) {

                pvidList.forEach(pvid -> {
                    //todo qid做成配置项
                    List<String> bizEqualIdList = elasticSearchService.aggregationQuery(region.getCode(), indexName, "qid", Map.of("appId", appId, "pvid", pvid));
                    log.debug("获取应用: {}, 区域: {}, 产品方案版本：{} 业务唯一标识列表: {}", appId, region.getCode(), pvid, bizEqualIdList);

                    bizEqualIdList.forEach(bizEqualId -> {
                        StatisticsConditionContext context = new StatisticsConditionContext()
                                .setAppId(appId)
                                .setRegion(region.getCode())
                                .setProductVersionId(pvid)
                                .setBizId(bizEqualId);
                        conditionContexts.add(context);
                    });
                });

            }
        }
        log.info("获取业务唯一标识查询条件列表: {} 条", conditionContexts.size());
        return conditionContexts;
    }

    private List<GeneratedMetaRegion> getRegions() {

        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        log.info("获取所有区域列表: {}", regions.stream().map(GeneratedMetaRegion::getCode).collect(Collectors.joining(",")));
        return regions;
//todo 调试完成后删除
//        List<GeneratedMetaRegion> regionList = regions.stream().filter(item -> item.getCode().equals("hf")).toList();
//        return regionList;
    }

    private List<CombineBurial> getBurialCombination(String indexName) {
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        ApiResponse apiResponse = metaDictFeign.getListByCode(BurialStatisticsModel.BURAIL_COMBINATION.getCode());
        List<GeneratedMetaDict> itemList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
        if (CollectionUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        List<CombineBurial> burialCombination = getBurialCombination(itemList);


        List<GeneratedMetaRegion> regions = getRegions();

        //在各区域中聚合查找  fromType scene flowName的可能值
        for (GeneratedMetaRegion region : regions) {
            fillCombineBurial(indexName, region.getCode(), burialCombination);
        }
        return burialCombination;
    }

    private List<CombineBurial> getBurialCombination(List<GeneratedMetaDict> itemList) {
        List<CombineBurial> combineBurials = new ArrayList<>();
        for (GeneratedMetaDict burialCombination : itemList) {
            String combination = burialCombination.getCode();
            CombineBurial combineBurial = new CombineBurial();
            combineBurial.setCode(combination);
            List<BurialType> burialTypes = new ArrayList<>();
            for (String burialCode : combination.split("_")) {
                BurialType burialType = new BurialType(burialCode);
                burialTypes.add(burialType);
            }
            combineBurial.setBurialTypes(burialTypes);
            combineBurials.add(combineBurial);
        }
        return combineBurials;
    }

    private void fillCombineBurial(String indexName, String region, List<CombineBurial> combineBurials) {
        for (CombineBurial combineBurial : combineBurials) {
            combineBurial.getBurialTypes().forEach(burialType -> {
                List<String> valueList = elasticSearchService.aggregationQuery(region, indexName, burialType.code);
                burialType.setValueList(valueList);
            });
        }
    }

    private void queryAndSave(String date, List<StatisticsConditionContext> statisticsConditionContextList) {

        for (StatisticsConditionContext conditionContext : statisticsConditionContextList) {
            List<QueryBuilder> queryBuilders = new ArrayList<>();
            queryBuilders.add(QueryBuilders.termQuery("type.keyword", "SearchAPI-Response"));
            queryBuilders.add(QueryBuilders.termQuery("appId", conditionContext.getAppId()));
            queryBuilders.add(QueryBuilders.termQuery("pvid", conditionContext.getProductVersionId()));
            queryBuilders.add(StatisticsConditionContext.buildQuerybuilder(conditionContext.getDynamicConditions()));
            long requestCount = elasticSearchService.countDocuments(conditionContext.getRegion(), DateUtil.getIndexName(date), queryBuilders);

            if (0 == requestCount) {
                log.debug("埋点动态分析数据统计,没有请求数据,日期: {}, region: {}, appId: {}, productId: {}, productVersionId: {}, burialCode: {}",
                        date,
                        conditionContext.getRegion(),
                        conditionContext.getAppId(),
                        conditionContext.getProductId(), conditionContext.getProductVersionId(), conditionContext.getBurialType());
                continue;
            }
            // 有结果查询
            queryBuilders.add(QueryBuilders.existsQuery("docsCount"));
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("docsCount");
            rangeQueryBuilder.gt(0);
            queryBuilders.add(rangeQueryBuilder);
            long hasResult = elasticSearchService.countDocuments(conditionContext.getRegion(), DateUtil.getIndexName(date), queryBuilders);

            BurialDynamicsAnalysis burialDynamicsAnalysis = BeanUtil.copyProperties(conditionContext, BurialDynamicsAnalysis.class);
            burialDynamicsAnalysis.setDate(date);
            burialDynamicsAnalysis.setBurialItem(StatisticsConditionContext.getDynamicMap(conditionContext.getDynamicConditions()));
            burialDynamicsAnalysis.setRequestCount(requestCount);
            burialDynamicsAnalysis.setHasResult(hasResult);
            burialDynamicsAnalysis.setNoResult(requestCount - hasResult);
            burialDynamicsAnalysis.setBurialCode(conditionContext.getBurialType());
            this.burialDynamicsRepository.save(burialDynamicsAnalysis);
        }
    }

    /**
     * todo 获取应用appId 是不是应该从es中聚合获取？
     *
     * @return
     */
    private List<String> getAppIds() {
        log.info("获取业务应用列表");
        List<GeneratedApplication> applications = applicationRepository.findUnDeleted();

        if (log.isDebugEnabled()) {
            applications.forEach(app -> log.debug("应用ID: {}, 应用名称: {}", app.getAppId(), app.getName()));
        }

        return applications.stream().map(GeneratedApplication::getAppId).toList();
    }

    /**
     * 生成笛卡尔积组合
     */
    private static List<List<Map.Entry<String, String>>> generateCartesianCombinations(List<BurialType> burialTypes) {
        List<List<Map.Entry<String, String>>> combinations = new ArrayList<>();
        if (burialTypes.isEmpty()) return combinations;
        combinations.add(new ArrayList<>());

        for (BurialType item : burialTypes) {
            List<List<Map.Entry<String, String>>> newCombinations = new ArrayList<>();
            for (List<Map.Entry<String, String>> existingCombination : combinations) {
                for (String value : item.getValueList()) {
                    List<Map.Entry<String, String>> newCombination = new ArrayList<>(existingCombination);
                    newCombination.add(new AbstractMap.SimpleEntry<>(item.getCode(), value));
                    newCombinations.add(newCombination);
                }
            }
            combinations = newCombinations;
        }
        return combinations;
    }

    /**
     * 清理业务应用流量历史数据
     *
     * @param date 日期 例如：2025-06-01
     */
    private void cleanBurialDynamicsHistoryData(String date) {
        this.burialDynamicsRepository.deleteByDate(date);
    }

    /**
     * 清理业务应用流量历史数据
     *
     * @param date 日期 例如：2025-06-01
     */
    private void cleanBizEqualIdHistoryData(String date) {
        this.bizEqualIdAnalysisRepository.deleteByDateStr(date);
    }


    /**
     * 清理业务应用流量历史数据
     *
     * @param date 日期 例如：2025-06-01
     */
    private void cleanAppHistoryData(String date) {
        List<AppAnalysis> existing = appAnalysisRepository.findByDate(date);
        if (CollectionUtil.isEmpty(existing)) {
            return;
        }

        List<Long> ids = existing.stream()
                .map(AppAnalysis::getId)
                .collect(Collectors.toList());

        appAnalysisRepository.deleteByDateEquals(date);
        flowAnalysisRepository.deleteByRefIdIn(ids);
    }

    /**
     * 清理埋点流量历史数据
     *
     * @param date 日期 例如：2025-06-01
     */
    private void cleanBurialHistoryData(String date) {
        List<BurialAnalysis> existing = burialAnalysisRepository.findByDate(date);
        if (CollectionUtil.isEmpty(existing)) {
            return;
        }

        List<Long> ids = existing.stream()
                .map(BurialAnalysis::getId)
                .collect(Collectors.toList());

        burialAnalysisRepository.deleteByDateEquals(date);
        flowAnalysisRepository.deleteByRefIdIn(ids);
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class QueryWithConditions {
        private final QueryBuilder queryBuilder;

        private final Map<String, Object> conditions;
    }

    @Setter
    @Getter
    private static class CombineBurial {
        /**
         * fromType_scene
         */
        private String code;
        private List<BurialType> burialTypes;

        // 笛卡尔积结果
        private List<List<Map.Entry<String, String>>> cartesian;
    }

    @Data
    private static class BurialType {
        /**
         * scene 或者 fromType。。。
         */
        private String code;
        private List<String> valueList;

        public BurialType(String code) {
            this.code = code;
            this.valueList = new ArrayList<>();
        }
    }
}
