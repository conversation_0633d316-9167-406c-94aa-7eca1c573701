package com.iflytek.lynxiao.support.repository.support;


import com.iflytek.lynxiao.support.repository.support.entity.BusinessEqualIdAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BusinessEqualIdAnalysisRepository extends JpaRepository<BusinessEqualIdAnalysis, Long> {


    void deleteByDateStr(String date);
}
