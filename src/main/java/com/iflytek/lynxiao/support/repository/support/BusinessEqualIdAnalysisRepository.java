package com.iflytek.lynxiao.support.repository.support;


import com.iflytek.lynxiao.support.repository.support.entity.BusinessEqualIdAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 */
@Repository
public interface BusinessEqualIdAnalysisRepository extends JpaRepository<BusinessEqualIdAnalysis, Long> {


//    @Query("DELETE FROM BusinessEqualIdAnalysis m WHERE m.da")
    void deleteByDateStr(String date);
}
