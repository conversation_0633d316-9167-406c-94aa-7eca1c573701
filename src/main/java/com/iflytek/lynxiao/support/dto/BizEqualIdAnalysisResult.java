package com.iflytek.lynxiao.support.dto;

import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Accessors(chain = true)
public class BizEqualIdAnalysisResult {
    /**
     * 应用id
     */
    private String appId;

    /**
     * 区域code
     */
    private String region;

    /**
     * 产品方案id
     */
    private Long productId;

    /**
     * 产品方案版本id
     */
    private String productVersionId;

    /**
     * 请求总数
     */
    private Long requestCount;

    /**
     * 有结果数
     */
    private Long hasResult;

    /**
     * 无结果数
     */
    private Long noResult;

    /**
     * 返回结果数汇总
     */
    private Long docsCountTotal;

    /**
     * 分布情况
     */
    private List<BizEqualIdResultDistribution> distribution = new ArrayList<>();
}
